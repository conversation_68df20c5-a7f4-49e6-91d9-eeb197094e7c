package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockCur;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName StockCurService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:36
 */
public interface StockCurService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月31日 上午9:50:36
     * <AUTHOR>
     */
    Integer save(StockCur record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月31日 上午9:50:36
     * <AUTHOR>
     */
    Integer update(StockCur record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:50:36
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return StockCur
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:50:36
     * <AUTHOR>
     */
    StockCur selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<StockCur>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月31日 上午9:50:36
     * <AUTHOR>
     */
    DataSet<StockCur> getDataSetList(Page page, StockCur record);

    void updateStock(List<StockCur> stockCurList);

    String getMd5(StockCur stockCur);

    List<StockCur> selectStockCurList(List<StockCur> stockCurList);
}
