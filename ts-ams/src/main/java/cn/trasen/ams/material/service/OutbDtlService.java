package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.OutbDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutbDtlService
 * @Description TODO
 * @date 2025年8月1日 下午4:43:50
 */
public interface OutbDtlService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年8月1日 下午4:43:50
     * <AUTHOR>
     */
    Integer save(OutbDtl record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年8月1日 下午4:43:50
     * <AUTHOR>
     */
    Integer update(OutbDtl record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年8月1日 下午4:43:50
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return OutbDtl
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年8月1日 下午4:43:50
     * <AUTHOR>
     */
    OutbDtl selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<OutbDtl>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年8月1日 下午4:43:50
     * <AUTHOR>
     */
    DataSet<OutbDtl> getDataSetList(Page page, OutbDtl record);

    /**
     * @param outbDtlList
     * @return void
     * @Title batchInsert
     * @Description 批量插入出库单明细
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    void batchInsert(List<OutbDtl> outbDtlList);

    /**
     * @param outbId
     * @return void
     * @Title deleteByOutbId
     * @Description 根据出库单ID删除明细
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    void deleteByOutbId(String outbId);

    /**
     * @param outbId
     * @return void
     * @Title _deleteByOutbId_
     * @Description 根据出库单ID物理删除明细
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    void _deleteByOutbId_(String outbId);

    /**
     * @param outbId
     * @return List<OutbDtl>
     * @Title getOutbDtlListByOutbId
     * @Description 根据出库单ID获取明细列表
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    List<OutbDtl> getOutbDtlListByOutbId(String outbId);

    /**
     * @param outbId
     * @return List<OutbDtlResp>
     * @Title getOutbDtlExtListByOutbId
     * @Description 根据出库单ID获取扩展明细列表
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    List<OutbDtlResp> getOutbDtlExtListByOutbId(String outbId, String name);


    /**
     * @param record
     * @return void
     * @Title dataFmt
     * @Description 数据格式化
     * @date 2025年8月1日 下午4:45:00
     * <AUTHOR>
     */
    void dataFmt(OutbDtlResp record);

    void fastOut(Inb inb, List<InbDtl> inbDtlList);
}
