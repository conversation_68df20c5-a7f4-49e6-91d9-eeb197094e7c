package cn.trasen.ams.material.controller;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.material.bean.outb.OutbDetailResp;
import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.ams.material.bean.outb.OutbInsertReq;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.model.Batch;
import cn.trasen.ams.material.model.MethodCode;
import cn.trasen.ams.material.service.BatchService;
import cn.trasen.ams.material.service.MethodCodeService;
import cn.trasen.ams.material.service.OutbDtlService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.service.OutbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutbController
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 */
@RestController
@Api(tags = "OutbController")
public class OutbController {

    private transient static final Logger logger = LoggerFactory.getLogger(OutbController.class);

    @Autowired
    private OutbService outbService;

    @Autowired
    private OutbDtlService outbDtlService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private MethodCodeService methodCodeService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveOutb
     * @Description 新增
     * @date 2025年8月1日 下午4:43:14
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单新增", notes = "物资出库单新增")
    @PostMapping("/api/material/outb/save")
    public PlatformResult<String> saveOutb(@Validated @RequestBody OutbInsertReq record) {
        try {
            String id = outbService.insert(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateOutb
     * @Description 编辑
     * @date 2025年8月1日 下午4:43:14
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单编辑", notes = "物资出库单编辑")
    @PostMapping("/api/material/outb/update")
    public PlatformResult<String> updateOutb(@Validated @RequestBody OutbInsertReq record) {
        try {
            String id = outbService.edit(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<OutbDetailResp>
     * @Title selectOutbById
     * @Description 根据ID查询
     * @date 2025年8月1日 下午4:43:14
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单详情", notes = "物资出库单详情")
    @GetMapping("/api/material/outb/{id}")
    public PlatformResult<OutbDetailResp> selectOutbById(@PathVariable String id, @RequestParam(value = "direction", defaultValue = "current") String direction, @RequestParam(required = false) String name, @RequestParam(required = false) String withBatch) {
        try {
            // 根据direction参数获取对应的出库单ID
            String targetId = outbService.getTargetOutbId(id, direction);
            if (targetId == null) {
                return PlatformResult.failure("没有找到" + getDirectionDesc(direction) + "的记录");
            }

            OutbDetailResp record = new OutbDetailResp();
            Outb outb = outbService.selectById(targetId);
            MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_CK, outb.getId());

            if (methodCode != null) {
                outb.setMtdCodeId(methodCode.getId());
                outb.setMtdCodeName(methodCode.getName());
            }


            List<OutbDtlResp> outbDtlList = outbDtlService.getOutbDtlExtListByOutbId(outb.getId(), name);

            if (CommonConst.YES.equals(withBatch)) {
                // outbDtlList 提取skuId
                List<String> skuIdList = outbDtlList.stream().map(OutbDtlResp::getSkuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                String whId = outb.getWhId();
                // 获取批次信息
                List<Batch> batchList = batchService.getBatchBySkuIdList(whId, skuIdList);
                Map<String, List<Batch>> batchMap = batchList.stream().collect(Collectors.groupingBy(Batch::getSkuId));
                // 将批次信息添加到出库明细中
                for (OutbDtlResp outbDtl : outbDtlList) {
                    List<Batch> batches = batchMap.get(outbDtl.getSkuId());
                    if (batches != null) {
                        outbDtl.setBatchList(batches);
                    }
                }
            }

            record.setOutb(outb);
            record.setOutbDtlList(outbDtlList);

            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * 获取方向描述
     *
     * @param direction 方向参数
     * @return 描述文本
     */
    private String getDirectionDesc(String direction) {
        switch (direction) {
            case "prev":
                return "上一条";
            case "next":
                return "下一条";
            case "current":
            default:
                return "当前";
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteOutbById
     * @Description 根据ID删除
     * @date 2025年8月1日 下午4:43:14
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单删除", notes = "物资出库单删除")
    @PostMapping("/api/material/outb/delete/{id}")
    public PlatformResult<String> deleteOutbById(@PathVariable String id) {
        try {
            outbService.remove(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param outbIdList
     * @return PlatformResult<String>
     * @Title batchDeleteOutb
     * @Description 批量删除出库单
     * @date 2025年8月1日 下午4:50:00
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单批量删除", notes = "物资出库单批量删除")
    @PostMapping("/api/material/outb/batch/delete")
    public PlatformResult<String> batchDeleteOutb(@RequestBody List<String> outbIdList) {
        try {
            // 参数验证
            if (CollectionUtils.isEmpty(outbIdList)) {
                return PlatformResult.failure("删除的出库单ID列表不能为空");
            }
            // 调用批量删除服务
            outbService.batchRemove(outbIdList);
            return PlatformResult.success("成功删除 " + outbIdList.size() + " 条出库单记录");
        } catch (Exception e) {
            logger.error("批量删除出库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Outb>
     * @Title selectOutbList
     * @Description 查询列表
     * @date 2025年8月1日 下午4:43:14
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单列表", notes = "物资出库单列表")
    @GetMapping("/api/material/outb/list")
    public DataSet<Outb> selectOutbList(Page page, Outb record) {
        return outbService.getDataSetList(page, record);
    }

    /**
     * @param outbIdList
     * @return PlatformResult<String>
     * @Title batchConfirmOutb
     * @Description 批量确认出库单
     * @date 2025年8月1日 下午4:50:00
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单批量确认", notes = "物资出库单批量确认")
    @PostMapping("/api/material/outb/batch/confirm")
    public PlatformResult<String> batchConfirmOutb(@RequestBody List<String> outbIdList) {
        try {
            // 参数验证
            if (CollectionUtils.isEmpty(outbIdList)) {
                return PlatformResult.failure("确认的出库单ID列表不能为空");
            }

            // 调用批量确认服务
            outbService.batchConfirm(outbIdList);
            return PlatformResult.success("成功确认 " + outbIdList.size() + " 条出库单记录");
        } catch (Exception e) {
            logger.error("批量确认出库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param outbIdList
     * @return PlatformResult<String>
     * @Title rollbackBatchConfirmOutb
     * @Description 批量撤销确认出库单
     * @date 2025年8月1日 下午4:50:00
     * <AUTHOR>
     */
    @ApiOperation(value = "物资出库单批量撤销确认", notes = "物资出库单批量撤销确认")
    @PostMapping("/api/material/outb/batch/rollback-confirm")
    public PlatformResult<String> rollbackBatchConfirmOutb(@RequestBody List<String> outbIdList) {
        try {
            // 参数验证
            if (CollectionUtils.isEmpty(outbIdList)) {
                return PlatformResult.failure("撤销确认的出库单ID列表不能为空");
            }

            // 调用批量撤销确认服务
            outbService.rollbackBatchConfirm(outbIdList);
            return PlatformResult.success("成功撤销确认 " + outbIdList.size() + " 条出库单记录");
        } catch (Exception e) {
            logger.error("批量撤销确认出库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


}
