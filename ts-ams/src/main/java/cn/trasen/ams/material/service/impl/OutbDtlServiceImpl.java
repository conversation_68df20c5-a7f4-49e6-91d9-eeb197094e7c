package cn.trasen.ams.material.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.ams.material.constant.MSkuConst;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.model.Outb;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.OutbDtlMapper;
import cn.trasen.ams.material.model.OutbDtl;
import cn.trasen.ams.material.service.OutbDtlService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutbDtlServiceImpl
 * @Description TODO
 * @date 2025年8月1日 下午4:43:50
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class OutbDtlServiceImpl implements OutbDtlService {

    private transient static final Logger logger = LoggerFactory.getLogger(OutbDtlServiceImpl.class);

    @Autowired
    private OutbDtlMapper mapper;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(OutbDtl record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(OutbDtl record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        OutbDtl record = new OutbDtl();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public OutbDtl selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<OutbDtl> getDataSetList(Page page, OutbDtl record) {
        Example example = new Example(OutbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<OutbDtl> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public void batchInsert(List<OutbDtl> outbDtlList) {
        // 填充插入时候的必要值
        if (CollectionUtils.isEmpty(outbDtlList)) {
            logger.warn("批量插入出库单明细时，传入的列表为空，操作被忽略.");
            return;
        }
        for (OutbDtl outbDtl : outbDtlList) {
            if (outbDtl.getId() == null) {
                outbDtl.setId(IdGeneraterUtils.nextId());
            }
            outbDtl.setCreateDate(new Date());
            outbDtl.setUpdateDate(new Date());
            outbDtl.setIsDeleted("N");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                outbDtl.setCreateUser(user.getUsercode());
                outbDtl.setCreateUserName(user.getUsername());
                outbDtl.setUpdateUser(user.getUsercode());
                outbDtl.setUpdateUserName(user.getUsername());
                outbDtl.setSsoOrgCode(user.getCorpcode());
                outbDtl.setSsoOrgName(user.getOrgName());
                outbDtl.setDeptId(user.getDeptId());
                outbDtl.setDeptName(user.getDeptname());
            }
        }

        mapper.batchInsert(outbDtlList);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByOutbId(String outbId) {
        // 逻辑删除
        Assert.hasText(outbId, "出库单ID不能为空.");
        Example example = new Example(OutbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outbId", outbId);

        OutbDtl record = new OutbDtl();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        // 执行逻辑删除
        int deletedCount = mapper.updateByExampleSelective(record, example);

        // 记录删除结果
        if (deletedCount > 0) {
            logger.info("成功逻辑删除出库单 {} 的 {} 条明细记录", outbId, deletedCount);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void _deleteByOutbId_(String outbId) {
        // 物理删除
        Assert.hasText(outbId, "出库单ID不能为空.");
        Example example = new Example(OutbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outbId", outbId);

        // 执行物理删除
        int deletedCount = mapper.deleteByExample(example);

        // 记录删除结果
        if (deletedCount > 0) {
            logger.info("成功删除出库单 {} 的 {} 条明细记录", outbId, deletedCount);
        }
    }

    @Override
    public List<OutbDtl> getOutbDtlListByOutbId(String outbId) {
        Example example = new Example(OutbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outbId", outbId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<OutbDtl> records = mapper.selectByExample(example);
        return records;
    }

    @Override
    public List<OutbDtlResp> getOutbDtlExtListByOutbId(String outbId, String name) {
        return mapper.getOutbDtlExtListByOutbId(outbId, name);
    }


    @Override
    public void dataFmt(OutbDtlResp record) {
        // 可以在这里添加数据格式化逻辑
        record.setUnitShow(dictService.cgetNameByValue(MSkuConst.SKU_UNIT, record.getUnit()));
    }

    @Override
    public void fastOut(Inb inb, List<InbDtl> inbDtlList) {

    }

    private Outb Inb2Outb(Inb inb) {

    }

    private OutbDtl Inb2OutbDtl(Inb inb) {
        
    }
}
