<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.BatchMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.Batch">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="prod_no" jdbcType="VARCHAR" property="prodNo"/>
        <result column="prod_date" jdbcType="DATE" property="prodDate"/>
        <result column="expire_date" jdbcType="DATE" property="expireDate"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO m_batch
        (
        id,
        inb_id,
        sku_id,
        price,
        batch_no,
        prod_no,
        prod_date,
        expire_date,
        create_date,
        create_user,
        create_user_name,
        dept_id,
        dept_name,
        update_date,
        update_user,
        update_user_name,
        sso_org_code,
        sso_org_name,
        is_deleted
        )
        VALUES
        <foreach collection="batchList" item="item" index="index" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.inbId,jdbcType=VARCHAR},
            #{item.skuId,jdbcType=VARCHAR},
            #{item.price,jdbcType=DECIMAL},
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.prodNo,jdbcType=VARCHAR},
            #{item.prodDate,jdbcType=DATE},
            #{item.expireDate,jdbcType=DATE},
            #{item.createDate,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.createUserName,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=VARCHAR},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.updateUser,jdbcType=VARCHAR},
            #{item.updateUserName,jdbcType=VARCHAR},
            #{item.ssoOrgCode,jdbcType=VARCHAR},
            #{item.ssoOrgName,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="getBatchBySkuIdList" resultType="cn.trasen.ams.material.model.Batch">
        select
        t1.*,
        COALESCE(t2.`num`,0) as latestNum,
        COALESCE(t3.`num`,0) as originNum
        from m_batch t1
        left join m_stock_cur t2 on t1.`batch_no` = t2.`batch_no` and t2.is_deleted = 'N'
        left join m_inb_dtl t3 on t1.`batch_no` = t3.`batch_no` and t3.is_deleted = 'N'
        left join m_inb t4 on t1.inb_id = t4.id and t4.is_deleted = 'N'
        where t1.is_deleted = 'N'
        and t4.stat = 1
        <if test="skuIdList != null and skuIdList.size() > 0">
            and t1.sku_id in
            <foreach item="item" index="index" collection="skuIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="whId != null and whId != ''">
            and t2.wh_id = #{whId}
        </if>
        order by t1.create_date desc
    </select>
</mapper>