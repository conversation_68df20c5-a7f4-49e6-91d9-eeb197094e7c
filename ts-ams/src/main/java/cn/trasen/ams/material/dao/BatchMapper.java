package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.Batch;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface BatchMapper extends Mapper<Batch> {
    void batchInsert(@Param("batchList") List<Batch> batchList);

    List<Batch> getBatchBySkuIdList(@Param("whId") String whId, @Param("skuIdList") List<String> skuIdList);
}